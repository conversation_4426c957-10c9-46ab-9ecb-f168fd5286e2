'use client';

import { usePathname } from 'next/navigation';
import { Locale, getOppositeLocale } from '../lib/i18n';

export function useLanguageSwitch(currentLocale: Locale) {
  const pathname = usePathname();

  const switchLanguage = () => {
    const newLocale = getOppositeLocale(currentLocale);
    
    // إنشاء المسار الجديد مع اللغة الجديدة
    let newPath = pathname;
    
    // إزالة اللغة الحالية من المسار
    if (pathname.startsWith(`/${currentLocale}`)) {
      newPath = pathname.replace(`/${currentLocale}`, '');
    }
    
    // إضافة اللغة الجديدة
    newPath = `/${newLocale}${newPath}`;
    
    // التأكد من أن المسار صحيح
    if (newPath === `/${newLocale}`) {
      newPath = `/${newLocale}`;
    }

    // حفظ تفضيل اللغة
    try {
      localStorage.setItem('preferredLocale', newLocale);
      document.cookie = `preferredLocale=${newLocale}; path=/; max-age=31536000`; // سنة واحدة
    } catch (error) {
      console.warn('Failed to save language preference:', error);
    }

    // الانتقال للصفحة الجديدة
    window.location.href = newPath;
  };

  const getLanguageSwitchUrl = () => {
    const newLocale = getOppositeLocale(currentLocale);
    let newPath = pathname;
    
    if (pathname.startsWith(`/${currentLocale}`)) {
      newPath = pathname.replace(`/${currentLocale}`, '');
    }
    
    return `/${newLocale}${newPath}`;
  };

  return {
    switchLanguage,
    getLanguageSwitchUrl,
    oppositeLocale: getOppositeLocale(currentLocale)
  };
}
