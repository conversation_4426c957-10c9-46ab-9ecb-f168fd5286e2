import { Metadata } from 'next';

import { ProductWithDetails, Category, Subcategory } from '@/types/mysql-database';

// تعريف نوع Locale محلياً
type Locale = 'ar' | 'en';
import ResponsiveProductDetailPage from '@/components/ResponsiveProductDetailPage';

interface ProductPageProps {
  params: Promise<{
    locale: Locale;
    id: string;
  }>;
}

// دالة لجلب بيانات المنتج من الخادم
async function fetchProductData(productId: string): Promise<{
  product: ProductWithDetails | null;
  category: Category | null;
  subcategory: Subcategory | null;
}> {
  try {
    console.log('🚀 Server: Fetching product details for ID:', productId);

    // جلب جميع البيانات في طلب واحد محسن
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/products/${productId}`, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'NextJS-Server/1.0',
      },
      next: { revalidate: 300 }, // cache لمدة 5 دقائق
    });

    if (!response.ok) {
      console.error(`❌ Server: Product API returned ${response.status}`);
      return { product: null, category: null, subcategory: null };
    }

    const result = await response.json();
    console.log('📦 Server: Product API response:', result);

    if (!result.success || !result.data) {
      console.error('❌ Server: Failed to fetch product details:', result);
      return { product: null, category: null, subcategory: null };
    }

    const { product, category, subcategory } = result.data;
    return { product, category, subcategory };
  } catch (error) {
    console.error('❌ Server: Error fetching product data:', error);
    return { product: null, category: null, subcategory: null };
  }
}

// دالة لإنشاء metadata للصفحة
export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const locale = (resolvedParams?.locale || 'ar') as Locale;
  const id = resolvedParams?.id || '';

  const { product } = await fetchProductData(id);

  if (!product) {
    return {
      title: locale === 'ar' ? 'المنتج غير موجود' : 'Product Not Found',
      description: locale === 'ar' ? 'المنتج المطلوب غير موجود' : 'The requested product was not found',
    };
  }

  const productTitle = locale === 'ar' ? product.title_ar : product.title;
  const productDescription = locale === 'ar' ? product.description_ar : product.description;

  // إنشاء الرابط الكامل للمنتج
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || process.env.NEXT_PUBLIC_APP_URL || 'https://droobhajer.com';
  const productUrl = `${baseUrl}/${locale}/product/${id}`;

  // تحويل روابط الصور المحلية إلى روابط كاملة
  const productImages = product.images && product.images.length > 0
    ? product.images.map(img => ({
        url: img.image_url.startsWith('http') ? img.image_url : `${baseUrl}${img.image_url}`,
        width: 1200,
        height: 630,
        alt: productTitle,
      }))
    : [{
        url: `${baseUrl}/placeholder-image.jpg`,
        width: 1200,
        height: 630,
        alt: productTitle,
      }];

  return {
    title: `${productTitle} | ${locale === 'ar' ? 'دروب هاجر' : 'Droob Hajer'}`,
    description: productDescription || (locale === 'ar' ? 'منتج عالي الجودة من دروب هاجر' : 'High quality product from Droob Hajer'),
    keywords: [
      productTitle,
      locale === 'ar' ? 'دروب هاجر' : 'Droob Hajer',
      ...(locale === 'ar' ? [
        'أطباق بوفيه', 'أطباق عرض', 'أطباق تقديم فاخرة', 'معدات بوفيه',
        'أدوات الضيافة', 'تجهيزات فندقية', 'أطباق مطاعم', 'أطباق فنادق',
        'معدات عرض الطعام', 'أطباق تقديم احترافية', 'مستلزمات البوفيه',
        'تجهيزات المطاعم الفاخرة', 'أدوات تقديم الطعام', 'معدات الضيافة'
      ] : [
        'buffet plates', 'buffet display', 'buffet platters', 'buffet equipment',
        'buffet bowls', 'display plates', 'serving plates', 'hospitality equipment',
        'hotel supplies', 'restaurant equipment', 'catering supplies', 'food display',
        'professional serving', 'luxury tableware', 'buffet accessories', 'hotel buffet'
      ])
    ],
    authors: [{ name: locale === 'ar' ? 'دروب هاجر' : 'Droob Hajer' }],
    openGraph: {
      title: productTitle,
      description: productDescription || (locale === 'ar' ? 'منتج عالي الجودة من دروب هاجر' : 'High quality product from Droob Hajer'),
      url: productUrl,
      siteName: locale === 'ar' ? 'دروب هاجر' : 'Droob Hajer',
      images: productImages,
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: productTitle,
      description: productDescription || (locale === 'ar' ? 'منتج عالي الجودة من دروب هاجر' : 'High quality product from Droob Hajer'),
      images: productImages,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

export default async function ProductDetailPage({ params }: ProductPageProps) {
  const resolvedParams = await params;
  const locale = (resolvedParams?.locale || 'ar') as Locale;
  const id = resolvedParams?.id || '';

  // جلب البيانات من الخادم
  const { product, category } = await fetchProductData(id);

  return (
    <div lang={locale} dir={locale === 'ar' ? 'rtl' : 'ltr'} className={`${locale === 'ar' ? 'rtl font-tajawal' : 'ltr font-inter'} min-h-screen`}>
      <ResponsiveProductDetailPage
        locale={locale}
        initialProduct={product}
        initialCategory={category}
        initialSubcategory={null}
        productId={id}
      />
    </div>
  );
}