'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import { Locale } from '../../lib/i18n';
import { ProductWithDetails, Category } from '../../types/mysql-database';
import { useInfiniteScroll } from '../../hooks/useInfiniteScroll';
import { useScrollPosition } from '../../hooks/useScrollPosition';
import MobileHeader from './MobileHeader';
import MobileBottomNav from './MobileBottomNav';
import MobileToast from './MobileToast';
import ProductSkeleton from '../ProductSkeleton';

// نوع عنصر السلة
interface CartItem {
  id: string;
  title: string;
  image: string;
  price: number;
  quantity: number;
}

interface MobileProductsPageProps {
  locale: Locale;
  initialProducts?: ProductWithDetails[];
  initialCategories?: Category[];
}

const MobileProductsPage: React.FC<MobileProductsPageProps> = ({
  locale,
  initialProducts,
  initialCategories
}) => {
  const searchParams = useSearchParams();

  const [products, setProducts] = useState<ProductWithDetails[]>(initialProducts || []);
  const [categories, setCategories] = useState<Category[]>(initialCategories || []);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('newest');
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMoreProducts, setHasMoreProducts] = useState(true);
  const [toast, setToast] = useState<{
    message: string;
    type: 'success' | 'error' | 'info';
    isVisible: boolean;
  }>({
    message: '',
    type: 'success',
    isVisible: false
  });

  // استخدام scroll position hook
  const scrollKey = `products-${locale}-${searchParams?.get('category') || 'all'}-${searchParams?.get('subcategory') || 'all'}`;
  const { restoreScrollPosition } = useScrollPosition(scrollKey);

  // الحصول على معاملات URL
  const categoryId = searchParams?.get('category');
  const subcategoryId = searchParams?.get('subcategory');
  const searchTerm = searchParams?.get('search');
  const shouldShowSearch = searchParams?.get('search') === 'true';

  // دالة تحميل المزيد من المنتجات
  const loadMoreProducts = useCallback(async () => {
    if (!hasMoreProducts || loading) return;

    try {
      // تحديد URL للمنتجات حسب الفلترة
      let productsUrl = `/api/products?page=${currentPage + 1}&limit=10`;
      if (subcategoryId) {
        productsUrl += `&subcategoryId=${subcategoryId}`;
      } else if (categoryId) {
        productsUrl += `&categoryId=${categoryId}`;
      }

      const response = await fetch(productsUrl, {
        headers: {
          'Cache-Control': 'max-age=300'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          setProducts(prev => [...prev, ...result.data]);
          setCurrentPage(prev => prev + 1);
          setHasMoreProducts(result.pagination?.hasMore || false);
        }
      }
    } catch (error) {
      console.error('Error loading more products:', error);
      showToastMessage(
        locale === 'ar' ? 'حدث خطأ في تحميل المزيد من المنتجات' : 'Error loading more products',
        'error'
      );
    }
  }, [currentPage, hasMoreProducts, loading, categoryId, subcategoryId, locale]);

  // استخدام infinite scroll hook
  const {
    isLoading: isLoadingMore,
    observerRef
  } = useInfiniteScroll(loadMoreProducts, {
    threshold: 100,
    enabled: hasMoreProducts && !loading
  });

  // جلب البيانات عند التحميل أو تغيير المعاملات
  useEffect(() => {
    const fetchData = async () => {
      // إذا كانت البيانات الأولية متوفرة ولا توجد معاملات فلترة، استخدمها
      if (initialProducts && !categoryId && !subcategoryId && !searchTerm) {
        setProducts(initialProducts);
        setCurrentPage(1);
        setHasMoreProducts(initialProducts.length >= 10);
        setLoading(false);

        // استعادة موضع التمرير بعد تأخير قصير
        setTimeout(() => {
          restoreScrollPosition();
        }, 100);
        return;
      }

      try {
        setLoading(true);
        setCurrentPage(1);

        // تحديد URL للمنتجات حسب الفلترة مع pagination
        let productsUrl = '/api/products?page=1&limit=10';
        if (subcategoryId) {
          productsUrl += `&subcategoryId=${subcategoryId}`;
        } else if (categoryId) {
          productsUrl += `&categoryId=${categoryId}`;
        }

        // جلب المنتجات
        const productsResponse = await fetch(productsUrl, {
          headers: {
            'Cache-Control': 'max-age=300' // كاش لمدة 5 دقائق
          }
        });
        if (productsResponse.ok) {
          const productsResult = await productsResponse.json();
          if (productsResult.success && productsResult.data) {
            setProducts(productsResult.data);
            setHasMoreProducts(productsResult.pagination?.hasMore || false);
          }
        }

        // جلب الفئات إذا لم تكن متوفرة
        if (!initialCategories) {
          const categoriesResponse = await fetch('/api/categories', {
            headers: {
              'Cache-Control': 'max-age=600' // كاش لمدة 10 دقائق
            }
          });
          if (categoriesResponse.ok) {
            const categoriesResult = await categoriesResponse.json();
            if (categoriesResult.success && Array.isArray(categoriesResult.data)) {
              setCategories(categoriesResult.data.filter((cat: Category) => cat.is_active));
            }
          }
        }

        // تعيين البحث من URL
        if (searchTerm && searchTerm !== 'true') {
          setSearchQuery(searchTerm);
          setShowSearch(true);
        } else if (shouldShowSearch) {
          setShowSearch(true);
        }

      } catch (error) {
        console.error('Error fetching data:', error);
        showToastMessage(
          locale === 'ar' ? 'حدث خطأ في تحميل البيانات' : 'Error loading data',
          'error'
        );
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [categoryId, subcategoryId, searchTerm, shouldShowSearch, initialCategories, initialProducts, locale, restoreScrollPosition]);

  // فلترة وترتيب المنتجات
  const filteredProducts = products.filter(product => {
    const matchesSearch = !searchQuery || 
      (locale === 'ar' ? product.title_ar : product.title)
        .toLowerCase().includes(searchQuery.toLowerCase()) ||
      (locale === 'ar' ? product.description_ar : product.description)
        ?.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || product.category_id === selectedCategory;
    
    return matchesSearch && matchesCategory;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return (a.price || 0) - (b.price || 0);
      case 'price-high':
        return (b.price || 0) - (a.price || 0);
      case 'name':
        const nameA = locale === 'ar' ? a.title_ar : a.title;
        const nameB = locale === 'ar' ? b.title_ar : b.title;
        return nameA.localeCompare(nameB);
      case 'newest':
      default:
        return new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime();
    }
  });

  const showToastMessage = (message: string, type: 'success' | 'error' | 'info' = 'success') => {
    setToast({ message, type, isVisible: true });
  };

  const hideToast = () => {
    setToast(prev => ({ ...prev, isVisible: false }));
  };

  const addToCart = (product: ProductWithDetails) => {
    if (!product.is_available) return;

    try {
      const cartItem: CartItem = {
        id: product.id,
        title: locale === 'ar' ? product.title_ar : product.title,
        image: product.images?.[0]?.image_url || '/placeholder-image.jpg',
        price: product.price || 0,
        quantity: 1
      };

      const existingCart = localStorage.getItem('cart');
      const cart = existingCart ? JSON.parse(existingCart) as CartItem[] : [];
      
      const existingItemIndex = cart.findIndex((item) => item.id === product.id);
      
      if (existingItemIndex > -1) {
        cart[existingItemIndex].quantity += 1;
      } else {
        cart.push(cartItem);
      }

      localStorage.setItem('cart', JSON.stringify(cart));
      
      // إرسال event لتحديث عداد السلة
      window.dispatchEvent(new Event('cartUpdated'));
      
      // إظهار رسالة نجاح
      showToastMessage(
        locale === 'ar' 
          ? 'تم إضافة المنتج للسلة بنجاح' 
          : 'Product added to cart successfully',
        'success'
      );
    } catch (error) {
      console.error('Error adding to cart:', error);
      showToastMessage(
        locale === 'ar' 
          ? 'حدث خطأ في إضافة المنتج للسلة' 
          : 'Error adding product to cart',
        'error'
      );
    }
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      const url = new URL(window.location.href);
      url.searchParams.set('search', searchQuery);
      window.history.pushState({}, '', url.toString());
    }
  };

  const clearSearch = () => {
    setSearchQuery('');
    const url = new URL(window.location.href);
    url.searchParams.delete('search');
    window.history.pushState({}, '', url.toString());
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header */}
      <MobileHeader
        locale={locale}
        title={locale === 'ar' ? 'المنتجات' : 'Products'}
        subtitle={`${filteredProducts.length} ${locale === 'ar' ? 'منتج' : 'products'}`}
        showBackButton={true}
        backUrl={`/${locale}`}
        showFilters={true}
        onFiltersToggle={() => setShowFilters(!showFilters)}
        customActions={
          <button
            onClick={() => setShowSearch(!showSearch)}
            className="w-9 h-9 bg-gray-100 rounded-full flex items-center justify-center"
          >
            <i className="ri-search-line text-gray-600"></i>
          </button>
        }
      />

      {/* Search Bar */}
      {showSearch && (
        <div className="px-4 py-3 bg-white border-b border-gray-100">
          <div className="flex items-center gap-2 animate-fadeInUp">
            <div className="flex-1 relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={locale === 'ar' ? 'ابحث عن المنتجات...' : 'Search products...'}
                className="w-full px-4 py-2 bg-gray-100 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
              <i className="ri-search-line absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
              {searchQuery && (
                <button
                  onClick={clearSearch}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                >
                  <i className="ri-close-line"></i>
                </button>
              )}
            </div>
            <button
              onClick={handleSearch}
              className="px-4 py-2 bg-primary text-white rounded-full text-sm font-medium"
            >
              {locale === 'ar' ? 'بحث' : 'Search'}
            </button>
          </div>
        </div>
      )}

      {/* Filters Panel */}
      {showFilters && (
        <div className="px-4 py-3 bg-white border-b border-gray-100 animate-fadeInUp">
          <div className="space-y-3">
            {/* Category Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {locale === 'ar' ? 'الفئة' : 'Category'}
              </label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 bg-gray-100 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
              >
                <option value="all">{locale === 'ar' ? 'جميع الفئات' : 'All Categories'}</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {locale === 'ar' ? category.name_ar : category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Sort Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {locale === 'ar' ? 'ترتيب حسب' : 'Sort by'}
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-3 py-2 bg-gray-100 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
              >
                <option value="newest">{locale === 'ar' ? 'الأحدث' : 'Newest'}</option>
                <option value="name">{locale === 'ar' ? 'الاسم' : 'Name'}</option>
                <option value="price-low">{locale === 'ar' ? 'السعر: من الأقل للأعلى' : 'Price: Low to High'}</option>
                <option value="price-high">{locale === 'ar' ? 'السعر: من الأعلى للأقل' : 'Price: High to Low'}</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Products Content */}
      <div className="px-4 py-4">
        {loading ? (
          <ProductSkeleton count={8} isMobile={true} />
        ) : filteredProducts.length > 0 ? (
          <div className="space-y-4">
            {filteredProducts.map((product) => (
              <div
                key={product.id}
                className="flex bg-white rounded-xl overflow-hidden shadow-sm"
              >
                <Link
                  href={`/${locale}/product/${product.id}`}
                  className="w-24 h-24 relative flex-shrink-0"
                  prefetch={true}
                >
                  {product.images?.[0]?.image_url ? (
                    <Image
                      src={product.images[0].image_url}
                      alt={locale === 'ar' ? product.title_ar : product.title}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                      <i className="ri-image-line text-gray-400"></i>
                    </div>
                  )}
                </Link>
                
                <div className="flex-1 p-3">
                  <Link href={`/${locale}/product/${product.id}`} prefetch={true}>
                    <h3 className="font-semibold text-gray-900 text-sm mb-1 line-clamp-1">
                      {locale === 'ar' ? product.title_ar : product.title}
                    </h3>
                    <p className="text-gray-600 text-xs mb-2 line-clamp-2">
                      {locale === 'ar' ? product.description_ar : product.description}
                    </p>
                  </Link>
                  
                  <div className="flex items-center justify-between">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      product.is_available
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {product.is_available 
                        ? (locale === 'ar' ? 'متوفر' : 'Available')
                        : (locale === 'ar' ? 'غير متوفر' : 'Unavailable')
                      }
                    </span>
                    
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        addToCart(product);
                      }}
                      disabled={!product.is_available}
                      className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                        product.is_available
                          ? 'bg-primary text-white hover:bg-primary/90 active:scale-95'
                          : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                      }`}
                    >
                      <i className="ri-shopping-cart-line"></i>
                    </button>
                  </div>
                </div>
              </div>
            ))}

            {/* Infinite Scroll Observer */}
            {hasMoreProducts && (
              <div ref={observerRef} className="py-6">
                {isLoadingMore && (
                  <div className="flex flex-col items-center space-y-3 animate-fadeIn">
                    {/* Beautiful loading animation */}
                    <div className="relative">
                      <div className="w-8 h-8 border-4 border-gray-200 rounded-full animate-spin border-t-primary"></div>
                      <div className="absolute inset-0 w-8 h-8 border-4 border-primary/20 rounded-full animate-ping"></div>
                    </div>

                    {/* Loading text with typing effect */}
                    <div className="text-center">
                      <span className="text-sm text-gray-600 font-medium">
                        {locale === 'ar' ? 'جاري تحميل المزيد' : 'Loading more'}
                      </span>
                      <span className="inline-block animate-pulse ml-1">...</span>
                    </div>

                    {/* Progress dots */}
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* End of products message */}
            {!hasMoreProducts && filteredProducts.length > 0 && (
              <div className="text-center py-8">
                <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <i className="ri-check-line text-gray-400 text-xl"></i>
                </div>
                <p className="text-sm text-gray-500">
                  {locale === 'ar' ? 'تم عرض جميع المنتجات' : 'All products loaded'}
                </p>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="ri-search-line text-gray-400 text-2xl"></i>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {locale === 'ar' ? 'لا توجد منتجات' : 'No products found'}
            </h3>
            <p className="text-gray-600 text-sm">
              {locale === 'ar'
                ? 'جرب تغيير معايير البحث أو الفلترة'
                : 'Try changing your search or filter criteria'
              }
            </p>
          </div>
        )}
      </div>

      {/* Bottom Navigation */}
      <MobileBottomNav locale={locale} />

      {/* Toast Notification */}
      <MobileToast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />
    </div>
  );
};

export default MobileProductsPage;
