'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Locale } from '../../lib/i18n';
import { useSiteSettings } from '../../hooks/useSiteSettings';

interface MobileHeaderProps {
  locale: Locale;
  title?: string;
  subtitle?: string;
  showBackButton?: boolean;
  backUrl?: string;
  onBackClick?: () => void;
  showFilters?: boolean;
  onFiltersToggle?: () => void;
  customActions?: React.ReactNode;
  useHistory?: boolean; // استخدام history.back() بدلاً من التنقل العادي
}

const MobileHeader: React.FC<MobileHeaderProps> = ({
  locale,
  title,
  subtitle,
  showBackButton = false,
  backUrl,
  onBackClick,
  showFilters = false,
  onFiltersToggle,
  customActions,
  useHistory = false
}) => {
  const { settings } = useSiteSettings();
  const [cartCount, setCartCount] = useState(0);

  // تحديث عداد السلة
  useEffect(() => {
    const updateCartCount = () => {
      try {
        const cart = localStorage.getItem('cart');
        if (cart) {
          const cartItems = JSON.parse(cart) as Array<{ id: string; quantity: number; title: string; image: string; price: number }>;
          const totalItems = cartItems.reduce((sum: number, item) => sum + (item.quantity || 1), 0);
          setCartCount(totalItems);
        }
      } catch (error) {
        console.error('Error reading cart:', error);
      }
    };

    updateCartCount();

    const handleStorageChange = () => updateCartCount();
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('cartUpdated', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('cartUpdated', handleStorageChange);
    };
  }, []);

  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick();
    } else if (useHistory) {
      // استخدام history.back() للحفاظ على الكاش وموضع التمرير
      window.history.back();
    } else if (backUrl) {
      // حفظ معلومة أن المستخدم يرجع من صفحة تفاصيل المنتج
      try {
        sessionStorage.setItem('returning_from_product', 'true');
      } catch {
        // تجاهل الخطأ
      }
      window.location.href = backUrl;
    } else {
      window.history.back();
    }
  };

  return (
    <div className="sticky top-0 bg-white shadow-sm z-50">
      <div className="px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Left Side - Logo/Back & Title */}
          <div className="flex items-center gap-3 flex-1 min-w-0">
            {showBackButton ? (
              <button
                onClick={handleBackClick}
                className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0"
              >
                <i className="ri-arrow-right-line text-gray-600"></i>
              </button>
            ) : (
              <Link
                href={`/${locale}`}
                className="flex items-center gap-2 flex-shrink-0"
              >
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">DH</span>
                </div>
              </Link>
            )}
            
            <div className="flex-1 min-w-0">
              {title ? (
                <>
                  <h1 className="font-bold text-gray-900 text-sm truncate">
                    {title}
                  </h1>
                  {subtitle && (
                    <p className="text-xs text-gray-500 truncate">
                      {subtitle}
                    </p>
                  )}
                </>
              ) : (
                <span className="font-bold text-gray-900">
                  {locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER'}
                </span>
              )}
            </div>
          </div>

          {/* Right Side - Actions */}
          <div className="flex items-center gap-2 flex-shrink-0">
            {/* Filters Button */}
            {showFilters && onFiltersToggle && (
              <button
                onClick={onFiltersToggle}
                className="w-9 h-9 bg-gray-100 rounded-full flex items-center justify-center"
              >
                <i className="ri-filter-line text-gray-600"></i>
              </button>
            )}

            {/* Custom Actions */}
            {customActions}

            {/* WhatsApp Button */}
            <a
              href={`https://wa.me/${settings?.communicationSettings?.whatsapp?.businessNumber || '+966501234567'}?text=${encodeURIComponent(locale === 'ar' ? 'مرحباً، أريد الاستفسار عن منتجاتكم' : 'Hello, I would like to inquire about your products')}`}
              target="_blank"
              rel="noopener noreferrer"
              className="w-9 h-9 bg-green-100 rounded-full flex items-center justify-center"
              title={locale === 'ar' ? 'تواصل عبر الواتساب' : 'Contact via WhatsApp'}
            >
              <i className="ri-whatsapp-line text-green-600"></i>
            </a>

            {/* Language Toggle */}
            <Link
              href={locale === 'ar' ? '/en' : '/ar'}
              className="w-9 h-9 bg-gray-100 rounded-full flex items-center justify-center"
              title={locale === 'ar' ? 'English' : 'العربية'}
            >
              <span className="text-xs font-bold text-gray-600">
                {locale === 'ar' ? 'EN' : 'ع'}
              </span>
            </Link>

            {/* Cart Button */}
            <Link
              href={`/${locale}/cart`}
              className="w-9 h-9 bg-gray-100 rounded-full flex items-center justify-center relative"
            >
              <i className="ri-shopping-cart-line text-gray-600"></i>
              {cartCount > 0 && (
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  {cartCount > 99 ? '99+' : cartCount}
                </span>
              )}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileHeader;
