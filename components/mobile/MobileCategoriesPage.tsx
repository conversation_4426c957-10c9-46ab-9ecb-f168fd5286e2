'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Locale } from '../../lib/i18n';
import { useScrollPosition } from '../../hooks/useScrollPosition';
import { useInfiniteScroll } from '../../hooks/useInfiniteScroll';
import MobileHeader from './MobileHeader';
import MobileBottomNav from './MobileBottomNav';
import MobileToast from './MobileToast';
import LoadingSpinner from '../LoadingSpinner';

// أنواع البيانات
interface Subcategory {
  id: string;
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
  image_url?: string;
  is_active: boolean;
  product_count: number;
}

interface Category {
  id: string;
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
  image_url?: string;
  is_active: boolean;
  subcategories: Subcategory[];
}

interface MobileCategoriesPageProps {
  locale: Locale;
  categories?: Category[];
}

const MobileCategoriesPage: React.FC<MobileCategoriesPageProps> = ({
  locale,
  categories: initialCategories
}) => {
  const [categories, setCategories] = useState<Category[]>(initialCategories || []);
  const [loading, setLoading] = useState(!initialCategories);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMoreCategories, setHasMoreCategories] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  // استخدام scroll position hook
  const scrollKey = `categories-${locale}`;
  const { restoreScrollPosition } = useScrollPosition(scrollKey);
  const [toast, setToast] = useState<{
    message: string;
    type: 'success' | 'error' | 'info';
    isVisible: boolean;
  }>({
    message: '',
    type: 'success',
    isVisible: false
  });

  // دالة تحميل المزيد من الفئات
  const loadMoreCategories = useCallback(async () => {
    if (!hasMoreCategories || loadingMore) return;

    try {
      setLoadingMore(true);

      const response = await fetch(`/api/categories?page=${currentPage + 1}&limit=6`, {
        headers: {
          'Cache-Control': 'max-age=600'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          setCategories(prev => [...prev, ...result.data]);
          setCurrentPage(prev => prev + 1);
          setHasMoreCategories(result.pagination?.hasMore || false);
        }
      }
    } catch (error) {
      console.error('Error loading more categories:', error);
      showToast(
        locale === 'ar' ? 'حدث خطأ في تحميل المزيد من الفئات' : 'Error loading more categories',
        'error'
      );
    } finally {
      setLoadingMore(false);
    }
  }, [currentPage, hasMoreCategories, loadingMore, locale]);

  // استخدام infinite scroll hook
  const {
    isLoading: isLoadingMore,
    observerRef
  } = useInfiniteScroll(loadMoreCategories, {
    threshold: 100,
    enabled: hasMoreCategories && !loadingMore
  });

  // جلب البيانات إذا لم يتم تمريرها
  useEffect(() => {
    if (!initialCategories) {
      const fetchCategories = async () => {
        try {
          const response = await fetch('/api/navbar/categories');
          const result = await response.json();
          if (result.success && Array.isArray(result.data)) {
            setCategories(result.data.filter((cat: Category) => cat.is_active));
          }
        } catch (error) {
          console.error('Error fetching categories:', error);
        } finally {
          setLoading(false);
        }
      };

      fetchCategories();
    }
  }, [initialCategories]);

  const hideToast = () => {
    setToast(prev => ({ ...prev, isVisible: false }));
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      window.location.href = `/${locale}/products?search=${encodeURIComponent(searchQuery)}`;
    }
  };

  const toggleCategory = (categoryId: string) => {
    setExpandedCategory(expandedCategory === categoryId ? null : categoryId);
  };

  // تصفية الفئات حسب البحث
  const filteredCategories = categories.filter(category => {
    if (!searchQuery.trim()) return true;
    const searchTerm = searchQuery.toLowerCase();
    const categoryName = (locale === 'ar' ? category.name_ar : category.name).toLowerCase();
    const subcategoryMatch = category.subcategories?.some(sub => 
      (locale === 'ar' ? sub.name_ar : sub.name).toLowerCase().includes(searchTerm)
    );
    return categoryName.includes(searchTerm) || subcategoryMatch;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <MobileHeader
          locale={locale}
          title={locale === 'ar' ? 'الفئات' : 'Categories'}
          showBackButton={true}
          backUrl={`/${locale}`}
          useHistory={true}
        />
        <LoadingSpinner
          size="lg"
          text={locale === 'ar' ? 'جاري تحميل الفئات...' : 'Loading categories...'}
          fullScreen={false}
          className="py-16"
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header */}
      <MobileHeader
        locale={locale}
        title={locale === 'ar' ? 'الفئات' : 'Categories'}
        subtitle={`${filteredCategories.length} ${locale === 'ar' ? 'فئة' : 'categories'}`}
        showBackButton={true}
        backUrl={`/${locale}`}
        customActions={
          <button
            onClick={() => setShowSearch(!showSearch)}
            className="w-9 h-9 bg-gray-100 rounded-full flex items-center justify-center"
          >
            <i className="ri-search-line text-gray-600"></i>
          </button>
        }
      />

      {/* Search Bar */}
      {showSearch && (
        <div className="px-4 py-3 bg-white border-b border-gray-100">
          <div className="flex items-center gap-2 animate-fadeInUp">
            <div className="flex-1 relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={locale === 'ar' ? 'ابحث في الفئات...' : 'Search categories...'}
                className="w-full px-4 py-2 bg-gray-100 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
              <i className="ri-search-line absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>
            <button
              onClick={handleSearch}
              className="px-4 py-2 bg-primary text-white rounded-full text-sm font-medium"
            >
              {locale === 'ar' ? 'بحث' : 'Search'}
            </button>
          </div>
        </div>
      )}

      {/* Page Title */}
      <div className="px-4 py-6 bg-gradient-to-r from-primary to-purple-600">
        <div className="text-center text-white">
          <h1 className="text-2xl font-bold mb-2">
            {locale === 'ar' ? 'تصفح الفئات' : 'Browse Categories'}
          </h1>
          <p className="text-sm opacity-90">
            {locale === 'ar' 
              ? 'اكتشف مجموعتنا المتنوعة من المنتجات' 
              : 'Discover our diverse range of products'
            }
          </p>
        </div>
      </div>

      {/* Categories List */}
      <div className="px-4 py-4">
        {filteredCategories.length > 0 ? (
          <div className="space-y-3">
            {filteredCategories.map((category) => (
              <div
                key={category.id}
                className="bg-white rounded-xl shadow-sm overflow-hidden"
              >
                {/* Category Header */}
                <div
                  onClick={() => toggleCategory(category.id)}
                  className="flex items-center p-4 cursor-pointer active:bg-gray-50"
                >
                  <div className="w-12 h-12 relative flex-shrink-0 mr-3">
                    {category.image_url ? (
                      <Image
                        src={category.image_url}
                        alt={locale === 'ar' ? category.name_ar : category.name}
                        fill
                        className="object-cover rounded-lg"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-100 rounded-lg flex items-center justify-center">
                        <i className="ri-folder-line text-gray-400"></i>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 mb-1">
                      {locale === 'ar' ? category.name_ar : category.name}
                    </h3>
                    <p className="text-xs text-gray-500">
                      {category.subcategories?.length || 0} {locale === 'ar' ? 'فئة فرعية' : 'subcategories'}
                    </p>
                  </div>
                  
                  <i className={`ri-arrow-down-s-line text-gray-400 transition-transform ${
                    expandedCategory === category.id ? 'rotate-180' : ''
                  }`}></i>
                </div>

                {/* Subcategories */}
                {expandedCategory === category.id && category.subcategories && (
                  <div className="border-t border-gray-100 bg-gray-50">
                    {category.subcategories.length > 0 ? (
                      <div className="p-3 space-y-2">
                        {category.subcategories.map((subcategory) => (
                          <Link
                            key={subcategory.id}
                            href={`/${locale}/subcategory/${subcategory.id}`}
                            className="flex items-center p-3 bg-white rounded-lg active:scale-95 transition-transform"
                          >
                            <div className="w-10 h-10 relative flex-shrink-0 mr-3">
                              {subcategory.image_url ? (
                                <Image
                                  src={subcategory.image_url}
                                  alt={locale === 'ar' ? subcategory.name_ar : subcategory.name}
                                  fill
                                  className="object-cover rounded-lg"
                                />
                              ) : (
                                <div className="w-full h-full bg-gray-100 rounded-lg flex items-center justify-center">
                                  <i className="ri-folder-2-line text-gray-400 text-sm"></i>
                                </div>
                              )}
                            </div>
                            
                            <div className="flex-1">
                              <h4 className="font-medium text-gray-800 text-sm">
                                {locale === 'ar' ? subcategory.name_ar : subcategory.name}
                              </h4>
                              <p className="text-xs text-gray-500">
                                {subcategory.product_count} {locale === 'ar' ? 'منتج' : 'products'}
                              </p>
                            </div>
                            
                            <i className="ri-arrow-left-line text-gray-400"></i>
                          </Link>
                        ))}
                      </div>
                    ) : (
                      <div className="p-4 text-center text-gray-500 text-sm">
                        {locale === 'ar' ? 'لا توجد فئات فرعية' : 'No subcategories'}
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}

            {/* Infinite Scroll Observer for Categories */}
            {hasMoreCategories && !searchQuery && (
              <div ref={observerRef} className="py-4">
                {(isLoadingMore || loadingMore) && (
                  <div className="flex flex-col items-center space-y-3 animate-fadeIn">
                    <div className="relative">
                      <div className="w-6 h-6 border-4 border-gray-200 rounded-full animate-spin border-t-primary"></div>
                      <div className="absolute inset-0 w-6 h-6 border-4 border-primary/20 rounded-full animate-ping"></div>
                    </div>
                    <div className="text-center">
                      <span className="text-sm text-gray-600 font-medium">
                        {locale === 'ar' ? 'جاري تحميل المزيد' : 'Loading more'}
                      </span>
                      <span className="inline-block animate-pulse ml-1">...</span>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* End of categories message */}
            {!hasMoreCategories && filteredCategories.length > 6 && !searchQuery && (
              <div className="text-center py-6">
                <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <i className="ri-check-line text-gray-400 text-lg"></i>
                </div>
                <p className="text-sm text-gray-500">
                  {locale === 'ar' ? 'تم عرض جميع الفئات' : 'All categories loaded'}
                </p>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-16">
            <i className="ri-folder-line text-6xl text-gray-400 mb-4"></i>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              {locale === 'ar' ? 'لا توجد فئات' : 'No categories found'}
            </h3>
            <p className="text-gray-600">
              {locale === 'ar'
                ? 'لم يتم العثور على فئات تطابق بحثك'
                : 'No categories match your search'
              }
            </p>
          </div>
        )}
      </div>

      {/* Bottom Navigation */}
      <MobileBottomNav locale={locale} />

      {/* Toast Notification */}
      <MobileToast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />
    </div>
  );
};

export default MobileCategoriesPage;
