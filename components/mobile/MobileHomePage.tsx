'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Locale } from '../../lib/i18n';
import { Category, ProductWithDetails } from '../../types/mysql-database';
import { useSiteSettings } from '../../hooks/useSiteSettings';
import { useScrollPosition } from '../../hooks/useScrollPosition';
import { useInfiniteScroll } from '../../hooks/useInfiniteScroll';
import MobileHeader from './MobileHeader';
import MobileBottomNav from './MobileBottomNav';
import MobileToast from './MobileToast';
import ProductSkeleton from '../ProductSkeleton';
import LoadingSpinner from '../LoadingSpinner';

// نوع عنصر السلة
interface CartItem {
  id: string;
  title: string;
  image: string;
  price: number;
  quantity: number;
}

interface MobileHomePageProps {
  locale: Locale;
  categories?: Category[];
  featuredProducts?: ProductWithDetails[];
}

const MobileHomePage: React.FC<MobileHomePageProps> = ({
  locale,
  categories: initialCategories,
  featuredProducts: initialProducts
}) => {
  const { settings } = useSiteSettings();
  const [categories, setCategories] = useState<Category[]>(initialCategories || []);
  const [featuredProducts, setFeaturedProducts] = useState<ProductWithDetails[]>(initialProducts || []);
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMoreProducts, setHasMoreProducts] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);

  // استخدام scroll position hook
  const scrollKey = `home-${locale}`;
  const { restoreScrollPosition } = useScrollPosition(scrollKey);
  const [toast, setToast] = useState<{
    message: string;
    type: 'success' | 'error' | 'info';
    isVisible: boolean;
  }>({
    message: '',
    type: 'success',
    isVisible: false
  });

  // دالة تحميل المزيد من المنتجات المميزة
  const loadMoreFeaturedProducts = useCallback(async () => {
    if (!hasMoreProducts || loadingMore) return;

    try {
      setLoadingMore(true);

      const response = await fetch(`/api/products?featured=true&page=${currentPage + 1}&limit=6`, {
        headers: {
          'Cache-Control': 'max-age=300'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          setFeaturedProducts(prev => [...prev, ...result.data]);
          setCurrentPage(prev => prev + 1);
          setHasMoreProducts(result.pagination?.hasMore || false);
        }
      }
    } catch (error) {
      console.error('Error loading more featured products:', error);
      showToast(
        locale === 'ar' ? 'حدث خطأ في تحميل المزيد من المنتجات' : 'Error loading more products',
        'error'
      );
    } finally {
      setLoadingMore(false);
    }
  }, [currentPage, hasMoreProducts, loadingMore, locale]);

  // استخدام infinite scroll hook
  const {
    isLoading: isLoadingMore,
    observerRef
  } = useInfiniteScroll(loadMoreFeaturedProducts, {
    threshold: 100,
    enabled: hasMoreProducts && !loadingMore
  });

  // Banner images from settings or default
  const bannerImages = settings?.heroImages?.filter(img => img.trim() !== '') || [
    'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=400&fit=crop',
    'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=800&h=400&fit=crop',
    'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=400&fit=crop'
  ];

  // Auto-rotate banner
  useEffect(() => {
    if (bannerImages.length > 1) {
      const interval = setInterval(() => {
        setCurrentBannerIndex((prev) => (prev + 1) % bannerImages.length);
      }, 4000);
      return () => clearInterval(interval);
    }
  }, [bannerImages.length]);

  // Fetch data if not provided
  useEffect(() => {
    if (!initialCategories) {
      console.log('🔄 Mobile: جلب الفئات من API...');
      fetch('/api/categories')
        .then(res => res.json())
        .then(result => {
          console.log('📦 Mobile: استجابة API الفئات:', result);
          if (result.success && Array.isArray(result.data)) {
            const activeCategories = result.data.filter((cat: Category) => cat.is_active);
            console.log('✅ Mobile: الفئات النشطة:', activeCategories.length);
            setCategories(activeCategories);
          }
        })
        .catch(error => {
          console.error('❌ Mobile: خطأ في جلب الفئات:', error);
        });
    } else {
      console.log('✅ Mobile: استخدام الفئات من props:', initialCategories.length);
      setCategories(initialCategories.filter((cat: Category) => cat.is_active));
    }

    if (!initialProducts) {
      fetch('/api/products?featured=true')
        .then(res => res.json())
        .then(result => {
          if (Array.isArray(result)) {
            setFeaturedProducts(result.slice(0, 6));
          } else if (result.success && Array.isArray(result.data)) {
            setFeaturedProducts(result.data.slice(0, 6));
          }
        })
        .catch(console.error);
    }
  }, [initialCategories, initialProducts]);

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'success') => {
    setToast({ message, type, isVisible: true });
  };

  const hideToast = () => {
    setToast(prev => ({ ...prev, isVisible: false }));
  };

  const addToCart = (product: ProductWithDetails) => {
    if (!product.is_available) return;

    try {
      const cartItem: CartItem = {
        id: product.id,
        title: locale === 'ar' ? product.title_ar : product.title,
        image: product.images?.[0]?.image_url || '/placeholder-image.jpg',
        price: product.price || 0,
        quantity: 1
      };

      const existingCart = localStorage.getItem('cart');
      const cart = existingCart ? JSON.parse(existingCart) as CartItem[] : [];
      
      const existingItemIndex = cart.findIndex((item) => item.id === product.id);
      
      if (existingItemIndex > -1) {
        cart[existingItemIndex].quantity += 1;
      } else {
        cart.push(cartItem);
      }

      localStorage.setItem('cart', JSON.stringify(cart));
      
      // إرسال event لتحديث عداد السلة
      window.dispatchEvent(new Event('cartUpdated'));
      
      // إظهار رسالة نجاح
      showToast(
        locale === 'ar' 
          ? 'تم إضافة المنتج للسلة بنجاح' 
          : 'Product added to cart successfully',
        'success'
      );
    } catch (error) {
      console.error('Error adding to cart:', error);
      showToast(
        locale === 'ar' 
          ? 'حدث خطأ في إضافة المنتج للسلة' 
          : 'Error adding product to cart',
        'error'
      );
    }
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      window.location.href = `/${locale}/products?search=${encodeURIComponent(searchQuery)}`;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header */}
      <MobileHeader
        locale={locale}
        customActions={
          <button
            onClick={() => setShowSearch(!showSearch)}
            className="w-9 h-9 bg-gray-100 rounded-full flex items-center justify-center"
          >
            <i className="ri-search-line text-gray-600"></i>
          </button>
        }
      />

      {/* Search Bar */}
      {showSearch && (
        <div className="px-4 py-3 bg-white border-b border-gray-100">
          <div className="flex items-center gap-2 animate-fadeInUp">
            <div className="flex-1 relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={locale === 'ar' ? 'ابحث عن المنتجات...' : 'Search products...'}
                className="w-full px-4 py-2 bg-gray-100 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
              <i className="ri-search-line absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>
            <button
              onClick={handleSearch}
              className="px-4 py-2 bg-primary text-white rounded-full text-sm font-medium"
            >
              {locale === 'ar' ? 'بحث' : 'Search'}
            </button>
          </div>
        </div>
      )}

      {/* Hero Banner */}
      <div className="relative h-48 overflow-hidden">
        {bannerImages.map((image, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentBannerIndex ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <Image
              src={image}
              alt={`Banner ${index + 1}`}
              fill
              className="object-cover"
              priority={index === 0}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
          </div>
        ))}
        
        {/* Banner Content */}
        <div className="absolute bottom-4 left-4 right-4 text-white">
          <h1 className="text-xl font-bold mb-1">
            {locale === 'ar' ? 'تجهيزات البوفيه الفاخرة' : 'Premium Buffet Equipment'}
          </h1>
          <p className="text-sm opacity-90">
            {locale === 'ar' ? 'أفضل المعدات لفندقك ومطعمك' : 'Best equipment for your hotel & restaurant'}
          </p>
        </div>

        {/* Banner Indicators */}
        {bannerImages.length > 1 && (
          <div className="absolute bottom-2 right-4 flex gap-1">
            {bannerImages.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentBannerIndex ? 'bg-white' : 'bg-white/50'
                }`}
              />
            ))}
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="px-4 py-4">
        <div className="grid grid-cols-4 gap-3">
          <Link
            href={`/${locale}/categories`}
            className="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm active:scale-95 transition-transform"
          >
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
              <i className="ri-grid-line text-blue-600 text-xl"></i>
            </div>
            <span className="text-xs font-medium text-gray-700 text-center">
              {locale === 'ar' ? 'الفئات' : 'Categories'}
            </span>
          </Link>

          <Link
            href={`/${locale}/products`}
            className="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm active:scale-95 transition-transform"
          >
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-2">
              <i className="ri-product-hunt-line text-green-600 text-xl"></i>
            </div>
            <span className="text-xs font-medium text-gray-700 text-center">
              {locale === 'ar' ? 'المنتجات' : 'Products'}
            </span>
          </Link>

          <Link
            href={`/${locale}/contact`}
            className="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm active:scale-95 transition-transform"
          >
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-2">
              <i className="ri-customer-service-line text-purple-600 text-xl"></i>
            </div>
            <span className="text-xs font-medium text-gray-700 text-center">
              {locale === 'ar' ? 'اتصل بنا' : 'Contact'}
            </span>
          </Link>

          <Link
            href={`/${locale}/about`}
            className="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm active:scale-95 transition-transform"
          >
            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-2">
              <i className="ri-information-line text-orange-600 text-xl"></i>
            </div>
            <span className="text-xs font-medium text-gray-700 text-center">
              {locale === 'ar' ? 'من نحن' : 'About'}
            </span>
          </Link>
        </div>
      </div>

      {/* Categories Section */}
      {categories.length > 0 && (
        <div className="px-4 pb-4">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-bold text-gray-900">
              {locale === 'ar' ? 'تصفح الفئات' : 'Browse Categories'}
            </h2>
            <Link
              href={`/${locale}/categories`}
              className="text-primary text-sm font-medium"
            >
              {locale === 'ar' ? 'عرض الكل' : 'View All'}
            </Link>
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            {categories.slice(0, 6).map((category) => (
              <Link
                key={category.id}
                href={`/${locale}/category/${category.id}`}
                className="bg-white rounded-xl overflow-hidden shadow-sm active:scale-95 transition-transform"
              >
                <div className="aspect-video relative">
                  {category.image_url ? (
                    <Image
                      src={category.image_url}
                      alt={locale === 'ar' ? category.name_ar : category.name}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                      <i className="ri-image-line text-gray-400 text-2xl"></i>
                    </div>
                  )}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                  <div className="absolute bottom-2 left-2 right-2">
                    <h3 className="text-white font-semibold text-sm">
                      {locale === 'ar' ? category.name_ar : category.name}
                    </h3>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Featured Products */}
      {featuredProducts.length > 0 && (
        <div className="px-4 pb-4">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-bold text-gray-900">
              {locale === 'ar' ? 'المنتجات المميزة' : 'Featured Products'}
            </h2>
            <Link
              href={`/${locale}/products`}
              className="text-primary text-sm font-medium"
            >
              {locale === 'ar' ? 'عرض الكل' : 'View All'}
            </Link>
          </div>
          
          <div className="space-y-3">
            {featuredProducts.map((product) => (
              <div
                key={product.id}
                className="flex bg-white rounded-xl overflow-hidden shadow-sm"
              >
                <Link
                  href={`/${locale}/product/${product.id}`}
                  className="w-20 h-20 relative flex-shrink-0"
                >
                  {product.images?.[0]?.image_url ? (
                    <Image
                      src={product.images[0].image_url}
                      alt={locale === 'ar' ? product.title_ar : product.title}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                      <i className="ri-image-line text-gray-400"></i>
                    </div>
                  )}
                </Link>
                
                <div className="flex-1 p-3">
                  <Link href={`/${locale}/product/${product.id}`}>
                    <h3 className="font-semibold text-gray-900 text-sm mb-1 line-clamp-1">
                      {locale === 'ar' ? product.title_ar : product.title}
                    </h3>
                    <p className="text-gray-600 text-xs mb-2 line-clamp-2">
                      {locale === 'ar' ? product.description_ar : product.description}
                    </p>
                  </Link>
                  
                  <div className="flex items-center justify-between">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      product.is_available
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {product.is_available 
                        ? (locale === 'ar' ? 'متوفر' : 'Available')
                        : (locale === 'ar' ? 'غير متوفر' : 'Unavailable')
                      }
                    </span>
                    
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        addToCart(product);
                      }}
                      disabled={!product.is_available}
                      className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                        product.is_available
                          ? 'bg-primary text-white hover:bg-primary/90 active:scale-95'
                          : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                      }`}
                    >
                      <i className="ri-shopping-cart-line"></i>
                    </button>
                  </div>
                </div>
              </div>
            ))}

            {/* Infinite Scroll Observer for Featured Products */}
            {hasMoreProducts && (
              <div ref={observerRef} className="py-4">
                {(isLoadingMore || loadingMore) && (
                  <div className="flex flex-col items-center space-y-3 animate-fadeIn">
                    <div className="relative">
                      <div className="w-6 h-6 border-4 border-gray-200 rounded-full animate-spin border-t-primary"></div>
                      <div className="absolute inset-0 w-6 h-6 border-4 border-primary/20 rounded-full animate-ping"></div>
                    </div>
                    <div className="text-center">
                      <span className="text-sm text-gray-600 font-medium">
                        {locale === 'ar' ? 'جاري تحميل المزيد' : 'Loading more'}
                      </span>
                      <span className="inline-block animate-pulse ml-1">...</span>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* End of products message */}
            {!hasMoreProducts && featuredProducts.length > 6 && (
              <div className="text-center py-6">
                <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <i className="ri-check-line text-gray-400 text-lg"></i>
                </div>
                <p className="text-sm text-gray-500">
                  {locale === 'ar' ? 'تم عرض جميع المنتجات المميزة' : 'All featured products loaded'}
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Bottom Navigation */}
      <MobileBottomNav locale={locale} />

      {/* Toast Notification */}
      <MobileToast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />
    </div>
  );
};

export default MobileHomePage;
