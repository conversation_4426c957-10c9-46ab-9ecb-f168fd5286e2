'use client';

import React, { useState, useEffect } from 'react';
import { Locale } from '../../lib/i18n';
import { useSiteSettings } from '../../hooks/useSiteSettings';
import { useScrollPosition } from '../../hooks/useScrollPosition';
import MobileHeader from './MobileHeader';
import MobileBottomNav from './MobileBottomNav';
import MobileToast from './MobileToast';
import LoadingSpinner from '../LoadingSpinner';

interface MobileContactPageProps {
  locale: Locale;
}

const MobileContactPage: React.FC<MobileContactPageProps> = ({ locale }) => {
  const { settings, loading } = useSiteSettings();

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(true);

  // استخدام scroll position hook
  const scrollKey = `contact-${locale}`;
  const { restoreScrollPosition } = useScrollPosition(scrollKey);

  // تحميل الصفحة
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsPageLoading(false);
      // استعادة موضع التمرير بعد التحميل
      setTimeout(() => {
        restoreScrollPosition();
      }, 100);
    }, 300);

    return () => clearTimeout(timer);
  }, [restoreScrollPosition]);
  const [toast, setToast] = useState<{
    message: string;
    type: 'success' | 'error' | 'info';
    isVisible: boolean;
  }>({
    message: '',
    type: 'success',
    isVisible: false
  });

  // استخدام الإعدادات من لوحة التحكم أو القيم الافتراضية
  const getContactContent = () => {
    if (!settings) {
      return {
        title: locale === 'ar' ? 'تواصل معنا' : 'Contact Us',
        subtitle: locale === 'ar' ? 'نحن هنا لمساعدتك في جميع استفساراتك واحتياجاتك' : 'We are here to help you with all your inquiries and needs',
        formTitle: locale === 'ar' ? 'أرسل لنا رسالة' : 'Send us a message',
        name: locale === 'ar' ? 'الاسم الكامل' : 'Full Name',
        email: locale === 'ar' ? 'البريد الإلكتروني' : 'Email Address',
        phone: locale === 'ar' ? 'رقم الهاتف' : 'Phone Number',
        subject: locale === 'ar' ? 'الموضوع' : 'Subject',
        message: locale === 'ar' ? 'الرسالة' : 'Message',
        send: locale === 'ar' ? 'إرسال الرسالة' : 'Send Message',
        address: locale === 'ar' ? 'العنوان' : 'Address',
        hours: locale === 'ar' ? 'ساعات العمل' : 'Working Hours',
        success: locale === 'ar' ? 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.' : 'Your message has been sent successfully! We will contact you soon.',
        error: locale === 'ar' ? 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.' : 'An error occurred while sending the message. Please try again.'
      };
    }

    return {
      title: locale === 'ar' ? 'تواصل معنا' : 'Contact Us',
      subtitle: locale === 'ar'
        ? 'نحن هنا لمساعدتك في جميع استفساراتك واحتياجاتك'
        : 'We are here to help you with all your inquiries and needs',
      formTitle: locale === 'ar' ? 'أرسل لنا رسالة' : 'Send us a message',
      name: locale === 'ar' ? 'الاسم الكامل' : 'Full Name',
      email: locale === 'ar' ? 'البريد الإلكتروني' : 'Email Address',
      phone: locale === 'ar' ? 'رقم الهاتف' : 'Phone Number',
      subject: locale === 'ar' ? 'الموضوع' : 'Subject',
      message: locale === 'ar' ? 'الرسالة' : 'Message',
      send: locale === 'ar' ? 'إرسال الرسالة' : 'Send Message',
      address: locale === 'ar' ? 'العنوان' : 'Address',
      hours: locale === 'ar' ? 'ساعات العمل' : 'Working Hours',
      success: locale === 'ar' ? 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.' : 'Your message has been sent successfully! We will contact you soon.',
      error: locale === 'ar' ? 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.' : 'An error occurred while sending the message. Please try again.'
    };
  };

  const currentContent = getContactContent();

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'success') => {
    setToast({ message, type, isVisible: true });
  };

  const hideToast = () => {
    setToast(prev => ({ ...prev, isVisible: false }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.email || !formData.message) {
      showToast(
        locale === 'ar' ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill all required fields',
        'error'
      );
      return;
    }

    setIsSubmitting(true);

    try {
      // محاكاة إرسال النموذج
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      showToast(currentContent.success, 'success');
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
      });
    } catch (error) {
      console.error('Error submitting form:', error);
      showToast(currentContent.error, 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (loading || isPageLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <MobileHeader
          locale={locale}
          title={locale === 'ar' ? 'تواصل معنا' : 'Contact Us'}
          showBackButton={true}
          backUrl={`/${locale}`}
          useHistory={true}
        />
        <LoadingSpinner
          size="lg"
          text={locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          fullScreen={false}
          className="py-16"
        />
        <MobileBottomNav locale={locale} />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header */}
      <MobileHeader
        locale={locale}
        title={currentContent.title}
        showBackButton={true}
        backUrl={`/${locale}`}
      />

      {/* Content */}
      <div className="px-4 py-4 space-y-6">
        {/* Hero Section */}
        <div className="bg-gradient-to-br from-primary to-primary/80 rounded-2xl p-6 text-white">
          <div className="text-center">
            <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
              <i className="ri-customer-service-line text-white text-xl"></i>
            </div>
            <h1 className="text-2xl font-bold mb-3">
              {currentContent.title}
            </h1>
            <p className="text-white/90 text-sm leading-relaxed">
              {currentContent.subtitle}
            </p>
          </div>
        </div>

        {/* Contact Information */}
        <div className="space-y-4">
          {/* Address */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <div className="flex items-start gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <i className="ri-map-pin-line text-blue-600"></i>
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-800 text-sm mb-1">{currentContent.address}</h3>
                <p className="text-gray-600 text-xs">
                  {locale === 'ar'
                    ? (settings?.addressAr || 'الرياض، المملكة العربية السعودية')
                    : (settings?.address || 'Riyadh, Saudi Arabia')
                  }
                </p>
              </div>
            </div>
          </div>

          {/* Phone */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <div className="flex items-start gap-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <i className="ri-phone-line text-green-600"></i>
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-800 text-sm mb-1">
                  {locale === 'ar' ? 'الهاتف' : 'Phone'}
                </h3>
                <a
                  href={`tel:${settings?.phone || '+966501234567'}`}
                  className="text-gray-600 text-xs hover:text-primary"
                >
                  {settings?.phone || '+966 50 123 4567'}
                </a>
              </div>
            </div>
          </div>

          {/* Email */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <div className="flex items-start gap-3">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <i className="ri-mail-line text-purple-600"></i>
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-800 text-sm mb-1">
                  {locale === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                </h3>
                <a
                  href={`mailto:${settings?.communicationSettings?.email?.adminEmail || '<EMAIL>'}`}
                  className="text-gray-600 text-xs hover:text-primary"
                >
                  {settings?.communicationSettings?.email?.adminEmail || '<EMAIL>'}
                </a>
              </div>
            </div>
          </div>

          {/* Working Hours */}
          <div className="bg-white rounded-xl p-4 shadow-sm">
            <div className="flex items-start gap-3">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <i className="ri-time-line text-orange-600"></i>
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-800 text-sm mb-1">{currentContent.hours}</h3>
                <p className="text-gray-600 text-xs">
                  {locale === 'ar'
                    ? (settings?.workingHoursAr || 'السبت - الخميس: 9:00 ص - 6:00 م')
                    : (settings?.workingHours || 'Saturday - Thursday: 9:00 AM - 6:00 PM')
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Contact Form */}
        <div className="bg-white rounded-xl p-6 shadow-sm">
          <h2 className="text-lg font-bold text-gray-800 mb-4">{currentContent.formTitle}</h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <input
                type="text"
                placeholder={currentContent.name}
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="w-full px-4 py-3 bg-gray-100 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
                required
              />
            </div>
            
            <div>
              <input
                type="email"
                placeholder={currentContent.email}
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="w-full px-4 py-3 bg-gray-100 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
                required
              />
            </div>
            
            <div>
              <input
                type="tel"
                placeholder={currentContent.phone}
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className="w-full px-4 py-3 bg-gray-100 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
              />
            </div>
            
            <div>
              <input
                type="text"
                placeholder={currentContent.subject}
                value={formData.subject}
                onChange={(e) => handleInputChange('subject', e.target.value)}
                className="w-full px-4 py-3 bg-gray-100 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
              />
            </div>
            
            <div>
              <textarea
                placeholder={currentContent.message}
                value={formData.message}
                onChange={(e) => handleInputChange('message', e.target.value)}
                rows={4}
                className="w-full px-4 py-3 bg-gray-100 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary/20 resize-none"
                required
              />
            </div>
            
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-primary text-white py-3 rounded-lg font-semibold disabled:opacity-50 flex items-center justify-center gap-2"
            >
              {isSubmitting ? (
                <>
                  <i className="ri-loader-4-line animate-spin"></i>
                  {locale === 'ar' ? 'جاري الإرسال...' : 'Sending...'}
                </>
              ) : (
                <>
                  <i className="ri-send-plane-line"></i>
                  {currentContent.send}
                </>
              )}
            </button>
          </form>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 gap-3">
          <a
            href={`https://wa.me/${settings?.communicationSettings?.whatsapp?.businessNumber || '+966501234567'}?text=${encodeURIComponent(locale === 'ar' ? 'مرحباً، أريد الاستفسار عن منتجاتكم' : 'Hello, I would like to inquire about your products')}`}
            target="_blank"
            rel="noopener noreferrer"
            className="bg-green-500 text-white p-4 rounded-xl text-center"
          >
            <i className="ri-whatsapp-line text-2xl mb-2 block"></i>
            <span className="text-sm font-medium">
              {locale === 'ar' ? 'واتساب' : 'WhatsApp'}
            </span>
          </a>
          
          <a
            href={`tel:${settings?.phone || '+966501234567'}`}
            className="bg-blue-500 text-white p-4 rounded-xl text-center"
          >
            <i className="ri-phone-line text-2xl mb-2 block"></i>
            <span className="text-sm font-medium">
              {locale === 'ar' ? 'اتصال' : 'Call'}
            </span>
          </a>
        </div>
      </div>

      {/* Bottom Navigation */}
      <MobileBottomNav locale={locale} />

      {/* Toast Notification */}
      <MobileToast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />
    </div>
  );
};

export default MobileContactPage;
